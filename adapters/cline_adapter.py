#!/usr/bin/env python3
"""
C<PERSON> (<PERSON>) to Sniffly Adapter

Converts Cline conversation data to Sniffly-compatible JSONL format.
"""

import json
import os
import glob
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ClineAdapter:
    """Converts Cline (<PERSON>) data to Sniffly format."""
    
    def __init__(self, cline_data_path: str):
        """
        Initialize the adapter.
        
        Args:
            cline_data_path: Path to Cline global storage directory
        """
        self.cline_data_path = Path(cline_data_path)
        self.output_dir = Path.home() / ".claude" / "projects"
        
    def find_cline_tasks(self) -> List[Path]:
        """Find all Cline task directories."""
        tasks_pattern = str(self.cline_data_path / "tasks" / "*")
        task_dirs = []
        
        for path in glob.glob(tasks_pattern):
            task_path = Path(path)
            if task_path.is_dir():
                # Check if it has conversation history
                history_file = task_path / "api_conversation_history.json"
                if history_file.exists():
                    task_dirs.append(task_path)
                    
        logger.info(f"Found {len(task_dirs)} Cline tasks with conversation history")
        return task_dirs
    
    def load_conversation_history(self, task_path: Path) -> Optional[List[Dict[str, Any]]]:
        """Load conversation history from a Cline task."""
        history_file = task_path / "api_conversation_history.json"
        
        try:
            with open(history_file, 'r') as f:
                history = json.load(f)
                return history if isinstance(history, list) else []
        except Exception as e:
            logger.warning(f"Failed to load conversation history from {history_file}: {e}")
            return None
    
    def convert_message_to_sniffly(self, message: Dict[str, Any], session_id: str, timestamp: str) -> Dict[str, Any]:
        """Convert a single Cline message to Sniffly format."""
        role = message.get('role', 'user')
        content = message.get('content', '')
        
        # Handle different content types
        if isinstance(content, str):
            content_blocks = [{"type": "text", "text": content}]
        elif isinstance(content, list):
            content_blocks = []
            for block in content:
                if isinstance(block, dict):
                    if block.get('type') == 'text':
                        content_blocks.append(block)
                    elif block.get('type') == 'tool_use':
                        # Convert tool use to text description
                        tool_name = block.get('name', 'unknown_tool')
                        tool_input = block.get('input', {})
                        content_blocks.append({
                            "type": "text",
                            "text": f"Used tool: {tool_name} with input: {json.dumps(tool_input, indent=2)}"
                        })
                elif isinstance(block, str):
                    content_blocks.append({"type": "text", "text": block})
        else:
            content_blocks = [{"type": "text", "text": str(content)}]
        
        # Map role to Sniffly format
        sniffly_type = "assistant" if role == "assistant" else "user"
        
        return {
            "timestamp": timestamp,
            "type": sniffly_type,
            "sessionId": session_id,
            "message": {
                "role": role,
                "content": content_blocks
            },
            "cwd": "",
            "uuid": f"cline-{session_id}-{timestamp}",
            "metadata": {
                "source": "cline",
                "original_role": role
            }
        }
    
    def convert_task_to_sniffly(self, task_path: Path) -> List[Dict[str, Any]]:
        """Convert a Cline task to Sniffly format."""
        conversation_history = self.load_conversation_history(task_path)
        
        if not conversation_history:
            return []
        
        messages = []
        session_id = task_path.name  # Use task directory name as session ID
        
        # Get task creation time as base timestamp
        try:
            task_timestamp = int(task_path.name)
            base_time = datetime.fromtimestamp(task_timestamp / 1000)
        except (ValueError, OSError):
            base_time = datetime.now()
        
        for i, message in enumerate(conversation_history):
            # Generate timestamp (base time + message index in seconds)
            message_time = base_time.timestamp() + i
            iso_timestamp = datetime.fromtimestamp(message_time).isoformat() + 'Z'
            
            sniffly_message = self.convert_message_to_sniffly(
                message, session_id, iso_timestamp
            )
            messages.append(sniffly_message)
        
        return messages
    
    def create_project_directory(self, project_name: str) -> Path:
        """Create a project directory for Sniffly."""
        project_path = self.output_dir / f"cline-{project_name}"
        project_path.mkdir(parents=True, exist_ok=True)
        return project_path
    
    def write_jsonl_file(self, messages: List[Dict[str, Any]], output_file: Path):
        """Write messages to JSONL file."""
        with open(output_file, 'w') as f:
            for message in messages:
                f.write(json.dumps(message) + '\n')
        
        logger.info(f"Wrote {len(messages)} messages to {output_file}")
    
    def convert_task(self, task_path: Path) -> Optional[str]:
        """Convert a single Cline task to Sniffly format."""
        try:
            messages = self.convert_task_to_sniffly(task_path)
            
            if not messages:
                logger.warning(f"No messages to convert from task {task_path.name}")
                return None
            
            # Create project directory
            project_name = task_path.name[:20]  # Truncate long names
            project_path = self.create_project_directory(project_name)
            
            # Write JSONL file
            output_file = project_path / "cline-conversation.jsonl"
            self.write_jsonl_file(messages, output_file)
            
            return str(project_path)
            
        except Exception as e:
            logger.error(f"Failed to convert task {task_path}: {e}")
            return None
    
    def convert_all(self) -> List[str]:
        """Convert all Cline tasks to Sniffly format."""
        tasks = self.find_cline_tasks()
        converted_projects = []
        
        for task in tasks:
            project_path = self.convert_task(task)
            if project_path:
                converted_projects.append(project_path)
        
        logger.info(f"Successfully converted {len(converted_projects)} Cline tasks")
        return converted_projects


def main():
    """Main function for command-line usage."""
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python cline_adapter.py <cline_data_path>")
        print("Example: python cline_adapter.py '/Users/<USER>/Library/Application Support/Code/User/globalStorage/saoudrizwan.claude-dev'")
        sys.exit(1)
    
    cline_data_path = sys.argv[1]
    
    if not os.path.exists(cline_data_path):
        print(f"Error: Path {cline_data_path} does not exist")
        sys.exit(1)
    
    adapter = ClineAdapter(cline_data_path)
    converted_projects = adapter.convert_all()
    
    print(f"\n✅ Conversion complete!")
    print(f"📊 Converted {len(converted_projects)} projects")
    print(f"📁 Projects saved to: {adapter.output_dir}")
    
    for project in converted_projects:
        print(f"   - {project}")


if __name__ == "__main__":
    main()
