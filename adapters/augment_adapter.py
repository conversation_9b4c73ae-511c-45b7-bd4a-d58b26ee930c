#!/usr/bin/env python3
"""
Augment to Sniffly Adapter

Converts Augment conversation data to Sniffly-compatible JSONL format.
"""

import json
import os
import glob
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AugmentAdapter:
    """Converts Augment data to Sniffly format."""
    
    def __init__(self, augment_data_path: str):
        """
        Initialize the adapter.
        
        Args:
            augment_data_path: Path to Augment workspace storage directory
        """
        self.augment_data_path = Path(augment_data_path)
        self.output_dir = Path.home() / ".claude" / "projects"
        
    def find_augment_workspaces(self) -> List[Path]:
        """Find all Augment workspace directories."""
        pattern = str(self.augment_data_path / "**/Augment.vscode-augment")
        workspaces = []
        
        for path in glob.glob(pattern, recursive=True):
            workspace_path = Path(path)
            if workspace_path.is_dir():
                workspaces.append(workspace_path)
                
        logger.info(f"Found {len(workspaces)} Augment workspaces")
        return workspaces
    
    def extract_conversation_data(self, workspace_path: Path) -> Dict[str, Any]:
        """Extract conversation data from an Augment workspace."""
        conversation_data = {}
        
        # Find agent edit shards
        shards_pattern = str(workspace_path / "augment-user-assets/agent-edits/shards/*.json")
        
        for shard_file in glob.glob(shards_pattern):
            try:
                with open(shard_file, 'r') as f:
                    shard_data = json.load(f)
                    
                conversation_id = shard_data.get('id', '').replace('shard-', '')
                if conversation_id:
                    conversation_data[conversation_id] = shard_data
                    
            except Exception as e:
                logger.warning(f"Failed to read shard file {shard_file}: {e}")
                
        return conversation_data
    
    def convert_to_sniffly_format(self, conversation_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Convert Augment conversation data to Sniffly JSONL format."""
        messages = []
        
        for conversation_id, shard_data in conversation_data.items():
            checkpoints = shard_data.get('checkpoints', {})
            
            for file_path, file_checkpoints in checkpoints.items():
                for checkpoint in file_checkpoints:
                    timestamp = checkpoint.get('timestamp', 0)
                    if timestamp == 0:
                        continue
                        
                    # Convert timestamp from milliseconds to ISO format
                    dt = datetime.fromtimestamp(timestamp / 1000)
                    iso_timestamp = dt.isoformat() + 'Z'
                    
                    # Extract file path info
                    doc_metadata = checkpoint.get('documentMetadata', {})
                    path_info = doc_metadata.get('path', {})
                    file_rel_path = path_info.get('relPath', 'unknown')
                    
                    # Determine if this is a user action or assistant edit
                    edit_source = checkpoint.get('editSource', 0)
                    source_tool_call = checkpoint.get('sourceToolCallRequestId', '')
                    
                    if edit_source == 1:  # Assistant edit
                        message = {
                            "timestamp": iso_timestamp,
                            "type": "assistant",
                            "sessionId": conversation_id,
                            "message": {
                                "role": "assistant",
                                "content": [
                                    {
                                        "type": "text",
                                        "text": f"Modified file: {file_rel_path}"
                                    }
                                ]
                            },
                            "cwd": "",
                            "uuid": f"augment-{conversation_id}-{timestamp}",
                            "metadata": {
                                "file_path": file_rel_path,
                                "tool_call_id": source_tool_call,
                                "edit_type": "file_modification"
                            }
                        }
                    else:  # User action or file view
                        message = {
                            "timestamp": iso_timestamp,
                            "type": "user",
                            "sessionId": conversation_id,
                            "message": {
                                "role": "user",
                                "content": [
                                    {
                                        "type": "text",
                                        "text": f"Requested action on file: {file_rel_path}"
                                    }
                                ]
                            },
                            "cwd": "",
                            "uuid": f"augment-{conversation_id}-{timestamp}",
                            "metadata": {
                                "file_path": file_rel_path,
                                "tool_call_id": source_tool_call,
                                "action_type": "file_request"
                            }
                        }
                    
                    messages.append(message)
        
        # Sort messages by timestamp
        messages.sort(key=lambda x: x['timestamp'])
        return messages
    
    def create_project_directory(self, project_name: str) -> Path:
        """Create a project directory for Sniffly."""
        project_path = self.output_dir / f"augment-{project_name}"
        project_path.mkdir(parents=True, exist_ok=True)
        return project_path
    
    def write_jsonl_file(self, messages: List[Dict[str, Any]], output_file: Path):
        """Write messages to JSONL file."""
        with open(output_file, 'w') as f:
            for message in messages:
                f.write(json.dumps(message) + '\n')
        
        logger.info(f"Wrote {len(messages)} messages to {output_file}")
    
    def convert_workspace(self, workspace_path: Path) -> Optional[str]:
        """Convert a single Augment workspace to Sniffly format."""
        try:
            # Extract conversation data
            conversation_data = self.extract_conversation_data(workspace_path)
            
            if not conversation_data:
                logger.warning(f"No conversation data found in {workspace_path}")
                return None
            
            # Convert to Sniffly format
            messages = self.convert_to_sniffly_format(conversation_data)
            
            if not messages:
                logger.warning(f"No messages to convert from {workspace_path}")
                return None
            
            # Create project directory
            workspace_name = workspace_path.parent.name[:20]  # Truncate long names
            project_path = self.create_project_directory(workspace_name)
            
            # Write JSONL file
            output_file = project_path / "augment-conversation.jsonl"
            self.write_jsonl_file(messages, output_file)
            
            return str(project_path)
            
        except Exception as e:
            logger.error(f"Failed to convert workspace {workspace_path}: {e}")
            return None
    
    def convert_all(self) -> List[str]:
        """Convert all Augment workspaces to Sniffly format."""
        workspaces = self.find_augment_workspaces()
        converted_projects = []
        
        for workspace in workspaces:
            project_path = self.convert_workspace(workspace)
            if project_path:
                converted_projects.append(project_path)
        
        logger.info(f"Successfully converted {len(converted_projects)} Augment workspaces")
        return converted_projects


def main():
    """Main function for command-line usage."""
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python augment_adapter.py <augment_data_path>")
        print("Example: python augment_adapter.py '/Users/<USER>/Library/Application Support/Cursor/User/workspaceStorage'")
        sys.exit(1)
    
    augment_data_path = sys.argv[1]
    
    if not os.path.exists(augment_data_path):
        print(f"Error: Path {augment_data_path} does not exist")
        sys.exit(1)
    
    adapter = AugmentAdapter(augment_data_path)
    converted_projects = adapter.convert_all()
    
    print(f"\n✅ Conversion complete!")
    print(f"📊 Converted {len(converted_projects)} projects")
    print(f"📁 Projects saved to: {adapter.output_dir}")
    
    for project in converted_projects:
        print(f"   - {project}")


if __name__ == "__main__":
    main()
