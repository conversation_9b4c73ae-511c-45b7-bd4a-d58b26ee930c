#!/usr/bin/env python3
"""
Multi-Tool Adapter for Sniffly

Unified adapter that can convert data from multiple AI coding assistants:
- GitHub Copilot
- Augment
- Cline (<PERSON>)
- And potentially others

This makes Sniffly a universal AI coding assistant analytics tool.
"""

import os
import sys
from pathlib import Path
from typing import List, Dict, Any
import logging

# Import our adapters
from augment_adapter import AugmentAdapter
from cline_adapter import ClineAdapter
from copilot_adapter import CopilotAdapter
from gemini_cli_adapter import GeminiCLIAdapter

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MultiToolAdapter:
    """Unified adapter for multiple AI coding assistants."""
    
    def __init__(self):
        """Initialize the multi-tool adapter."""
        self.output_dir = Path.home() / ".claude" / "projects"
        self.adapters = {}
        
    def detect_available_tools(self) -> Dict[str, str]:
        """Auto-detect available AI coding assistant data."""
        detected_tools = {}
        
        # Common paths for different tools
        base_paths = [
            Path.home() / "Library" / "Application Support",
            Path.home() / ".config",
            Path.home() / ".local" / "share"
        ]
        
        for base_path in base_paths:
            if not base_path.exists():
                continue
                
            # Check for Copilot
            copilot_paths = [
                base_path / "Code" / "User" / "globalStorage" / "github.copilot-chat",
                base_path / "Cursor" / "User" / "globalStorage" / "github.copilot-chat"
            ]
            
            for copilot_path in copilot_paths:
                if copilot_path.exists():
                    detected_tools['copilot'] = str(copilot_path)
                    break
            
            # Check for Cline (Claude Dev)
            cline_paths = [
                base_path / "Code" / "User" / "globalStorage" / "saoudrizwan.claude-dev",
                base_path / "Cursor" / "User" / "globalStorage" / "saoudrizwan.claude-dev"
            ]
            
            for cline_path in cline_paths:
                if cline_path.exists():
                    detected_tools['cline'] = str(cline_path)
                    break
            
            # Check for Augment
            augment_paths = [
                base_path / "Code" / "User" / "workspaceStorage",
                base_path / "Cursor" / "User" / "workspaceStorage"
            ]

            for augment_path in augment_paths:
                if augment_path.exists():
                    # Check if there are any Augment workspaces
                    augment_workspaces = list(augment_path.glob("*/Augment.vscode-augment"))
                    if augment_workspaces:
                        detected_tools['augment'] = str(augment_path)
                        break

        # Check for Gemini CLI (usually in ~/.gemini)
        gemini_path = Path.home() / ".gemini"
        if gemini_path.exists():
            tmp_dir = gemini_path / "tmp"
            if tmp_dir.exists() and any(tmp_dir.iterdir()):
                detected_tools['gemini-cli'] = str(gemini_path)
        
        logger.info(f"Detected tools: {list(detected_tools.keys())}")
        return detected_tools
    
    def convert_tool_data(self, tool_name: str, data_path: str) -> List[str]:
        """Convert data from a specific tool."""
        converted_projects = []
        
        try:
            if tool_name == 'copilot':
                adapter = CopilotAdapter(data_path)
                converted_projects = adapter.convert_all()
                
            elif tool_name == 'cline':
                adapter = ClineAdapter(data_path)
                converted_projects = adapter.convert_all()
                
            elif tool_name == 'augment':
                adapter = AugmentAdapter(data_path)
                converted_projects = adapter.convert_all()

            elif tool_name == 'gemini-cli':
                adapter = GeminiCLIAdapter(data_path)
                converted_projects = adapter.convert_all()

            else:
                logger.warning(f"Unknown tool: {tool_name}")
                
        except Exception as e:
            logger.error(f"Failed to convert {tool_name} data: {e}")
            
        return converted_projects
    
    def convert_all_detected_tools(self) -> Dict[str, List[str]]:
        """Convert data from all detected tools."""
        detected_tools = self.detect_available_tools()
        results = {}
        
        for tool_name, data_path in detected_tools.items():
            logger.info(f"Converting {tool_name} data from {data_path}")
            converted_projects = self.convert_tool_data(tool_name, data_path)
            results[tool_name] = converted_projects
            
        return results
    
    def create_unified_summary(self, results: Dict[str, List[str]]):
        """Create a unified summary of all converted data."""
        summary_file = self.output_dir / "multi-tool-summary.json"
        
        summary = {
            "conversion_date": "2025-01-28",
            "tools_converted": list(results.keys()),
            "total_projects": sum(len(projects) for projects in results.values()),
            "projects_by_tool": {
                tool: len(projects) for tool, projects in results.items()
            },
            "project_paths": results
        }
        
        import json
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
            
        logger.info(f"Created unified summary: {summary_file}")
        return summary


def main():
    """Main function for command-line usage."""
    print("🔧 Multi-Tool Adapter for Sniffly")
    print("Converting AI coding assistant data to Sniffly format...\n")
    
    adapter = MultiToolAdapter()
    
    # Auto-detect and convert all tools
    results = adapter.convert_all_detected_tools()
    
    if not results:
        print("❌ No AI coding assistant data found!")
        print("\nSearched for:")
        print("  - GitHub Copilot")
        print("  - Cline (Claude Dev)")
        print("  - Augment")
        print("  - Gemini CLI")
        sys.exit(1)
    
    # Create unified summary
    summary = adapter.create_unified_summary(results)
    
    # Display results
    print("✅ Conversion complete!\n")
    print(f"📊 Total projects converted: {summary['total_projects']}")
    print(f"📁 Projects saved to: {adapter.output_dir}\n")
    
    for tool, projects in results.items():
        if projects:
            print(f"🔧 {tool.upper()}:")
            for project in projects:
                print(f"   - {project}")
            print()
    
    print("🚀 You can now use Sniffly to analyze all your AI coding assistant data!")
    print(f"   Run: sniffly init")


if __name__ == "__main__":
    main()
