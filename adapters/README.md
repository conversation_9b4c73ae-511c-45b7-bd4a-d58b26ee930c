# Sniffly Multi-Tool Adapters

This directory contains adapters that make <PERSON>ni<PERSON><PERSON> work with multiple AI coding assistants, transforming it into a **universal AI coding assistant analytics tool**.

## 🔧 Supported Tools

| Tool | Data Type | Status | Description |
|------|-----------|--------|-------------|
| **GitHub Copilot** | Usage Analytics | ✅ Working | Command and setting usage patterns |
| **Augment** | Full Conversations | ✅ Working | Complete conversation history with file edits |
| **Cline (Claude Dev)** | Full Conversations | ✅ Working | Task-based conversation history |
| **Gemini CLI** | User Prompts + Placeholders | ✅ Working | User prompts with placeholder responses |
| **Standard Claude Code** | Full Conversations | ✅ Native | Original Sniffly format |

## 🚀 Quick Start

### Option 1: Auto-Install (Recommended)
```bash
# From the sniffly root directory
python install_adapters.py
```

### Option 2: Manual Conversion
```bash
cd adapters

# Convert all tools automatically
python multi_tool_adapter.py

# Or convert individual tools
python augment_adapter.py "/path/to/augment/data"
python cline_adapter.py "/path/to/cline/data"
python copilot_adapter.py "/path/to/copilot/data"
```

## 📊 What Gets Converted

### Augment
- **Source**: `~/Library/Application Support/Cursor/User/workspaceStorage/*/Augment.vscode-augment/`
- **Data**: File edit history, tool calls, conversation context
- **Output**: JSONL files with timestamped conversation flows

### Cline (Claude Dev)
- **Source**: `~/Library/Application Support/Code/User/globalStorage/saoudrizwan.claude-dev/tasks/`
- **Data**: API conversation history, task-based interactions
- **Output**: JSONL files with conversation messages

### GitHub Copilot
- **Source**: `~/Library/Application Support/*/globalStorage/github.copilot-chat/`
- **Data**: Command embeddings, setting configurations
- **Output**: Usage analytics (no conversation logs available)

### Gemini CLI
- **Source**: `~/.gemini/tmp/*/logs.json`
- **Data**: User prompts and session information
- **Output**: JSONL files with user prompts and placeholder assistant responses

## 📁 Output Structure

All converted data is saved to `~/.claude/projects/` in Sniffly-compatible format:

```
~/.claude/projects/
├── augment-{workspace-id}/
│   └── augment-conversation.jsonl
├── cline-{task-id}/
│   └── cline-conversation.jsonl
├── copilot-usage-analytics/
│   ├── copilot-usage.jsonl
│   └── usage-summary.json
├── gemini-cli-{session-id}/
│   ├── gemini-cli-conversation.jsonl
│   └── session-summary.json
└── multi-tool-summary.json
```

## 🔍 Data Format

All adapters convert data to Sniffly's JSONL format:

```json
{
  "timestamp": "2025-01-28T10:00:00Z",
  "type": "user|assistant",
  "sessionId": "unique-session-id",
  "message": {
    "role": "user|assistant",
    "content": [{"type": "text", "text": "message content"}]
  },
  "metadata": {
    "source": "augment|cline|copilot",
    "additional_fields": "..."
  }
}
```

## 🛠️ Individual Adapter Usage

### Augment Adapter
```python
from augment_adapter import AugmentAdapter

adapter = AugmentAdapter("/path/to/cursor/workspaceStorage")
projects = adapter.convert_all()
```

### Cline Adapter
```python
from cline_adapter import ClineAdapter

adapter = ClineAdapter("/path/to/claude-dev/globalStorage")
projects = adapter.convert_all()
```

### Copilot Adapter
```python
from copilot_adapter import CopilotAdapter

adapter = CopilotAdapter("/path/to/copilot/globalStorage")
projects = adapter.convert_all()
```

### Gemini CLI Adapter
```python
from gemini_cli_adapter import GeminiCLIAdapter

adapter = GeminiCLIAdapter("~/.gemini")
projects = adapter.convert_all()
```

## 🔧 Adding New Tools

To add support for a new AI coding assistant:

1. **Create a new adapter** (e.g., `new_tool_adapter.py`)
2. **Implement the required methods**:
   - `find_data_files()` - Locate tool data
   - `convert_to_sniffly_format()` - Transform to JSONL
   - `convert_all()` - Main conversion method
3. **Add to multi_tool_adapter.py**:
   - Update `detect_available_tools()`
   - Add conversion logic in `convert_tool_data()`

## 📈 Analytics Features

Once converted, you can use Sniffly to analyze:

- **Conversation patterns** across different AI tools
- **File modification history** and context
- **Tool usage statistics** and preferences
- **Time-based activity** patterns
- **Cross-tool comparison** of AI assistant usage

## 🐛 Troubleshooting

### No Projects Found
- Check if AI tools are installed and have been used
- Verify data paths are correct for your system
- Look for log messages indicating missing data

### Conversion Errors
- Ensure you have read permissions for data directories
- Check that Python has access to the required paths
- Review log output for specific error messages

### Sniffly Not Showing Projects
- Restart Sniffly after running adapters: `sniffly init`
- Check `~/.claude/projects/` contains converted data
- Verify JSONL files are properly formatted

## 📝 Notes

- **GitHub Copilot**: Only usage analytics available (no conversation logs)
- **Augment**: Rich conversation data with file edit context
- **Cline**: Task-based conversation history
- **Gemini CLI**: User prompts only (assistant responses are placeholders)
- **Privacy**: All data stays local on your machine
- **Compatibility**: Works with VS Code, Cursor, and other editors

## 🎯 Future Enhancements

Planned support for:
- **Cursor AI** (native conversations)
- **Gemini CLI** (command-line interactions)
- **CodeWhisperer** (usage analytics)
- **Tabnine** (completion statistics)
- **Custom tools** (plugin system)

---

**Made with ❤️ for the AI coding community**

Transform Sniffly into your universal AI coding assistant analytics dashboard!
