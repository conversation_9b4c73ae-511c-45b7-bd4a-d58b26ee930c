#!/usr/bin/env python3
"""
GitHub Copilot to Sniffly Adapter

Converts GitHub Copilot usage data to Sniffly-compatible analytics format.
Note: Copilot doesn't store conversation logs, so this creates usage analytics.
"""

import json
import os
import glob
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CopilotAdapter:
    """Converts GitHub Copilot data to Sniffly format."""
    
    def __init__(self, copilot_data_path: str):
        """
        Initialize the adapter.
        
        Args:
            copilot_data_path: Path to Copilot global storage directory
        """
        self.copilot_data_path = Path(copilot_data_path)
        self.output_dir = Path.home() / ".claude" / "projects"
        
    def find_copilot_data_files(self) -> Dict[str, Path]:
        """Find Copilot data files."""
        data_files = {}
        
        # Look for embedding files
        command_embeddings = self.copilot_data_path / "commandEmbeddings.json"
        setting_embeddings = self.copilot_data_path / "settingEmbeddings.json"
        
        if command_embeddings.exists():
            data_files['command_embeddings'] = command_embeddings
            
        if setting_embeddings.exists():
            data_files['setting_embeddings'] = setting_embeddings
            
        logger.info(f"Found {len(data_files)} Copilot data files")
        return data_files
    
    def load_embeddings_data(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """Load embeddings data from a Copilot file."""
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
                return data
        except Exception as e:
            logger.warning(f"Failed to load embeddings from {file_path}: {e}")
            return None
    
    def convert_command_embeddings_to_usage(self, embeddings_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Convert command embeddings to usage analytics."""
        messages = []
        base_time = datetime.now()
        
        # Extract command usage from embeddings
        for category, commands in embeddings_data.items():
            if isinstance(commands, dict):
                for command, data in commands.items():
                    if isinstance(data, dict) and 'embedding' in data:
                        # Create a usage message for each command
                        timestamp = base_time.isoformat() + 'Z'
                        
                        message = {
                            "timestamp": timestamp,
                            "type": "user",
                            "sessionId": "copilot-usage",
                            "message": {
                                "role": "user",
                                "content": [
                                    {
                                        "type": "text",
                                        "text": f"Used Copilot command: {command}"
                                    }
                                ]
                            },
                            "cwd": "",
                            "uuid": f"copilot-{command}-{timestamp}",
                            "metadata": {
                                "source": "copilot",
                                "command": command,
                                "category": category,
                                "embedding_size": len(data['embedding']) if 'embedding' in data else 0,
                                "usage_type": "command"
                            }
                        }
                        messages.append(message)
                        
                        # Increment time for next message
                        base_time = datetime.fromtimestamp(base_time.timestamp() + 1)
        
        return messages
    
    def create_usage_analytics(self, data_files: Dict[str, Path]) -> List[Dict[str, Any]]:
        """Create usage analytics from Copilot data."""
        all_messages = []
        
        # Process command embeddings
        if 'command_embeddings' in data_files:
            embeddings_data = self.load_embeddings_data(data_files['command_embeddings'])
            if embeddings_data:
                command_messages = self.convert_command_embeddings_to_usage(embeddings_data)
                all_messages.extend(command_messages)
        
        # Process setting embeddings (similar approach)
        if 'setting_embeddings' in data_files:
            embeddings_data = self.load_embeddings_data(data_files['setting_embeddings'])
            if embeddings_data:
                # Create setting usage messages
                base_time = datetime.now()
                for category, settings in embeddings_data.items():
                    if isinstance(settings, dict):
                        for setting, data in settings.items():
                            if isinstance(data, dict):
                                timestamp = base_time.isoformat() + 'Z'
                                
                                message = {
                                    "timestamp": timestamp,
                                    "type": "assistant",
                                    "sessionId": "copilot-usage",
                                    "message": {
                                        "role": "assistant",
                                        "content": [
                                            {
                                                "type": "text",
                                                "text": f"Copilot setting configured: {setting}"
                                            }
                                        ]
                                    },
                                    "cwd": "",
                                    "uuid": f"copilot-{setting}-{timestamp}",
                                    "metadata": {
                                        "source": "copilot",
                                        "setting": setting,
                                        "category": category,
                                        "usage_type": "setting"
                                    }
                                }
                                all_messages.append(message)
                                base_time = datetime.fromtimestamp(base_time.timestamp() + 1)
        
        # Sort messages by timestamp
        all_messages.sort(key=lambda x: x['timestamp'])
        return all_messages
    
    def create_project_directory(self, project_name: str) -> Path:
        """Create a project directory for Sniffly."""
        project_path = self.output_dir / f"copilot-{project_name}"
        project_path.mkdir(parents=True, exist_ok=True)
        return project_path
    
    def write_jsonl_file(self, messages: List[Dict[str, Any]], output_file: Path):
        """Write messages to JSONL file."""
        with open(output_file, 'w') as f:
            for message in messages:
                f.write(json.dumps(message) + '\n')
        
        logger.info(f"Wrote {len(messages)} messages to {output_file}")
    
    def create_summary_file(self, messages: List[Dict[str, Any]], summary_file: Path):
        """Create a summary of Copilot usage."""
        command_count = len([m for m in messages if m['metadata'].get('usage_type') == 'command'])
        setting_count = len([m for m in messages if m['metadata'].get('usage_type') == 'setting'])
        
        summary = {
            "total_interactions": len(messages),
            "command_usage": command_count,
            "setting_configurations": setting_count,
            "analysis_date": datetime.now().isoformat(),
            "note": "This data represents Copilot command and setting usage, not conversation logs"
        }
        
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        logger.info(f"Created usage summary: {summary_file}")
    
    def convert_all(self) -> List[str]:
        """Convert Copilot data to Sniffly format."""
        data_files = self.find_copilot_data_files()
        
        if not data_files:
            logger.warning("No Copilot data files found")
            return []
        
        # Create usage analytics
        messages = self.create_usage_analytics(data_files)
        
        if not messages:
            logger.warning("No usage data to convert")
            return []
        
        # Create project directory
        project_path = self.create_project_directory("usage-analytics")
        
        # Write JSONL file
        output_file = project_path / "copilot-usage.jsonl"
        self.write_jsonl_file(messages, output_file)
        
        # Create summary file
        summary_file = project_path / "usage-summary.json"
        self.create_summary_file(messages, summary_file)
        
        logger.info(f"Successfully converted Copilot data to {project_path}")
        return [str(project_path)]


def main():
    """Main function for command-line usage."""
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python copilot_adapter.py <copilot_data_path>")
        print("Example: python copilot_adapter.py '/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat'")
        sys.exit(1)
    
    copilot_data_path = sys.argv[1]
    
    if not os.path.exists(copilot_data_path):
        print(f"Error: Path {copilot_data_path} does not exist")
        sys.exit(1)
    
    adapter = CopilotAdapter(copilot_data_path)
    converted_projects = adapter.convert_all()
    
    print(f"\n✅ Conversion complete!")
    print(f"📊 Converted {len(converted_projects)} projects")
    print(f"📁 Projects saved to: {adapter.output_dir}")
    
    for project in converted_projects:
        print(f"   - {project}")
    
    print(f"\n📝 Note: GitHub Copilot doesn't store conversation logs.")
    print(f"   This adapter creates usage analytics from command and setting data.")


if __name__ == "__main__":
    main()
