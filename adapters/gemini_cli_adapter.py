#!/usr/bin/env python3
"""
Gemini CLI to Sniffly Adapter

Converts Gemini CLI conversation data to Sniffly-compatible JSONL format.
Note: Gemini CLI typically only stores user prompts, not assistant responses.
"""

import json
import os
import glob
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class GeminiCLIAdapter:
    """Converts Gemini CLI data to Sniffly format."""
    
    def __init__(self, gemini_data_path: str = None):
        """
        Initialize the adapter.
        
        Args:
            gemini_data_path: Path to Gemini CLI data directory (defaults to ~/.gemini)
        """
        if gemini_data_path is None:
            gemini_data_path = str(Path.home() / ".gemini")
        
        self.gemini_data_path = Path(gemini_data_path)
        self.output_dir = Path.home() / ".claude" / "projects"
        
    def find_gemini_sessions(self) -> List[Path]:
        """Find all Gemini CLI session directories."""
        tmp_dir = self.gemini_data_path / "tmp"
        session_dirs = []
        
        if not tmp_dir.exists():
            logger.warning(f"Gemini tmp directory not found: {tmp_dir}")
            return []
        
        for session_dir in tmp_dir.iterdir():
            if session_dir.is_dir():
                logs_file = session_dir / "logs.json"
                if logs_file.exists():
                    session_dirs.append(session_dir)
                    
        logger.info(f"Found {len(session_dirs)} Gemini CLI sessions")
        return session_dirs
    
    def load_session_logs(self, session_dir: Path) -> Optional[List[Dict[str, Any]]]:
        """Load logs from a Gemini CLI session."""
        logs_file = session_dir / "logs.json"
        
        try:
            with open(logs_file, 'r') as f:
                logs = json.load(f)
                return logs if isinstance(logs, list) else []
        except Exception as e:
            logger.warning(f"Failed to load logs from {logs_file}: {e}")
            return None
    
    def convert_to_sniffly_format(self, logs: List[Dict[str, Any]], session_name: str) -> List[Dict[str, Any]]:
        """Convert Gemini CLI logs to Sniffly JSONL format."""
        messages = []
        
        # Group messages by sessionId to handle multiple conversations in one file
        sessions = {}
        for log_entry in logs:
            session_id = log_entry.get('sessionId', 'unknown')
            if session_id not in sessions:
                sessions[session_id] = []
            sessions[session_id].append(log_entry)
        
        for session_id, session_logs in sessions.items():
            # Sort by messageId to ensure correct order
            session_logs.sort(key=lambda x: x.get('messageId', 0))
            
            for log_entry in session_logs:
                timestamp = log_entry.get('timestamp', '')
                message_text = log_entry.get('message', '')
                message_type = log_entry.get('type', 'user')
                message_id = log_entry.get('messageId', 0)
                
                # Convert to Sniffly format
                sniffly_message = {
                    "timestamp": timestamp,
                    "type": message_type,
                    "sessionId": session_id,
                    "message": {
                        "role": message_type,
                        "content": [
                            {
                                "type": "text",
                                "text": message_text
                            }
                        ]
                    },
                    "cwd": "",
                    "uuid": f"gemini-{session_id}-{message_id}",
                    "metadata": {
                        "source": "gemini-cli",
                        "session_name": session_name,
                        "message_id": message_id,
                        "original_session_id": session_id
                    }
                }
                
                messages.append(sniffly_message)
                
                # Since Gemini CLI typically only stores user prompts,
                # we can create a placeholder assistant response for better analytics
                if message_type == "user" and message_text.strip():
                    assistant_message = {
                        "timestamp": timestamp,  # Same timestamp since we don't have the actual response time
                        "type": "assistant",
                        "sessionId": session_id,
                        "message": {
                            "role": "assistant",
                            "content": [
                                {
                                    "type": "text",
                                    "text": "[Gemini CLI Response - Content not logged]"
                                }
                            ]
                        },
                        "cwd": "",
                        "uuid": f"gemini-{session_id}-{message_id}-response",
                        "metadata": {
                            "source": "gemini-cli",
                            "session_name": session_name,
                            "message_id": f"{message_id}-response",
                            "original_session_id": session_id,
                            "note": "Placeholder response - Gemini CLI doesn't log assistant responses"
                        }
                    }
                    messages.append(assistant_message)
        
        # Sort all messages by timestamp
        messages.sort(key=lambda x: x['timestamp'])
        return messages
    
    def create_project_directory(self, project_name: str) -> Path:
        """Create a project directory for Sniffly."""
        project_path = self.output_dir / f"gemini-cli-{project_name}"
        project_path.mkdir(parents=True, exist_ok=True)
        return project_path
    
    def write_jsonl_file(self, messages: List[Dict[str, Any]], output_file: Path):
        """Write messages to JSONL file."""
        with open(output_file, 'w') as f:
            for message in messages:
                f.write(json.dumps(message) + '\n')
        
        logger.info(f"Wrote {len(messages)} messages to {output_file}")
    
    def create_session_summary(self, messages: List[Dict[str, Any]], summary_file: Path, session_name: str):
        """Create a summary of the Gemini CLI session."""
        user_messages = [m for m in messages if m['type'] == 'user']
        assistant_messages = [m for m in messages if m['type'] == 'assistant']
        
        # Extract unique session IDs
        session_ids = list(set(m['sessionId'] for m in messages))
        
        # Get date range
        timestamps = [m['timestamp'] for m in messages if m['timestamp']]
        date_range = {
            "start": min(timestamps) if timestamps else None,
            "end": max(timestamps) if timestamps else None
        }
        
        summary = {
            "session_name": session_name,
            "total_messages": len(messages),
            "user_prompts": len(user_messages),
            "assistant_responses": len(assistant_messages),
            "unique_sessions": len(session_ids),
            "session_ids": session_ids,
            "date_range": date_range,
            "analysis_date": datetime.now().isoformat(),
            "note": "Gemini CLI typically only logs user prompts. Assistant responses are placeholders for analytics."
        }
        
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        logger.info(f"Created session summary: {summary_file}")
    
    def convert_session(self, session_dir: Path) -> Optional[str]:
        """Convert a single Gemini CLI session to Sniffly format."""
        try:
            logs = self.load_session_logs(session_dir)
            
            if not logs:
                logger.warning(f"No logs found in session {session_dir.name}")
                return None
            
            # Convert to Sniffly format
            session_name = session_dir.name[:20]  # Truncate long session names
            messages = self.convert_to_sniffly_format(logs, session_name)
            
            if not messages:
                logger.warning(f"No messages to convert from session {session_dir.name}")
                return None
            
            # Create project directory
            project_path = self.create_project_directory(session_name)
            
            # Write JSONL file
            output_file = project_path / "gemini-cli-conversation.jsonl"
            self.write_jsonl_file(messages, output_file)
            
            # Create summary file
            summary_file = project_path / "session-summary.json"
            self.create_session_summary(messages, summary_file, session_name)
            
            return str(project_path)
            
        except Exception as e:
            logger.error(f"Failed to convert session {session_dir}: {e}")
            return None
    
    def convert_all(self) -> List[str]:
        """Convert all Gemini CLI sessions to Sniffly format."""
        sessions = self.find_gemini_sessions()
        converted_projects = []
        
        for session in sessions:
            project_path = self.convert_session(session)
            if project_path:
                converted_projects.append(project_path)
        
        logger.info(f"Successfully converted {len(converted_projects)} Gemini CLI sessions")
        return converted_projects


def main():
    """Main function for command-line usage."""
    import sys
    
    gemini_data_path = None
    if len(sys.argv) > 1:
        gemini_data_path = sys.argv[1]
        if not os.path.exists(gemini_data_path):
            print(f"Error: Path {gemini_data_path} does not exist")
            sys.exit(1)
    
    adapter = GeminiCLIAdapter(gemini_data_path)
    converted_projects = adapter.convert_all()
    
    print(f"\n✅ Conversion complete!")
    print(f"📊 Converted {len(converted_projects)} Gemini CLI sessions")
    print(f"📁 Projects saved to: {adapter.output_dir}")
    
    for project in converted_projects:
        print(f"   - {project}")
    
    print(f"\n📝 Note: Gemini CLI typically only logs user prompts.")
    print(f"   Assistant responses are created as placeholders for analytics.")


if __name__ == "__main__":
    main()
