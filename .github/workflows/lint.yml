name: Lint

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  lint:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e .
        pip install -r requirements-dev.txt
    
    - name: Run ruff
      continue-on-error: true
      run: |
        ruff check sniffly/ sniffly-site/ --exclude="*/build.py"
    
    - name: Check formatting with ruff
      continue-on-error: true
      run: |
        ruff format sniffly/ sniffly-site/ --exclude="*/build.py" --check --diff
    
    - name: Run mypy
      continue-on-error: true
      run: |
        mypy sniffly/ sniffly-site/ --exclude="build.py" --ignore-missing-imports
    
    # JavaScript linting temporarily disabled
    # - name: Setup Node.js
    #   uses: actions/setup-node@v4
    #   with:
    #     node-version: '18'
    # 
    # - name: Install JavaScript dependencies
    #   run: |
    #     npm install
    # 
    # - name: Run ESLint
    #   continue-on-error: true
    #   run: |
    #     npx eslint sniffly/static/js/ sniffly-site/static/js/ sniffly-site/functions/