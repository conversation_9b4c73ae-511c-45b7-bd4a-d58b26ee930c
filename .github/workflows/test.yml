name: Tests

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10", "3.11", "3.12"]

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e .
        pip install -r requirements-dev.txt
    
    - name: Run tests
      run: |
        # Skip performance tests in CI as they require specific test data
        python -m pytest tests/ -v --tb=short --cov=sniffly --cov-report=term-missing --ignore=tests/sniffly/test_performance.py
    
    - name: Check test coverage
      run: |
        # Fail if coverage is below 60%
        coverage report --fail-under=60