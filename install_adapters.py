#!/usr/bin/env python3
"""
Sniffly Multi-Tool Adapter Installer

This script installs and runs the multi-tool adapters to make Sniffly work with:
- GitHub Copilot
- Augment
- Cline (<PERSON>)
- And potentially other AI coding assistants

Usage: python install_adapters.py
"""

import os
import sys
import subprocess
from pathlib import Path


def main():
    """Install and run the multi-tool adapters."""
    print("🚀 Sniffly Multi-Tool Adapter Installer")
    print("=" * 50)
    
    # Check if we're in the right directory
    current_dir = Path.cwd()
    adapters_dir = current_dir / "adapters"
    
    if not adapters_dir.exists():
        print("❌ Error: adapters directory not found!")
        print(f"   Current directory: {current_dir}")
        print("   Please run this script from the sniffly root directory.")
        sys.exit(1)
    
    # Check if adapter files exist
    required_files = [
        "adapters/multi_tool_adapter.py",
        "adapters/augment_adapter.py", 
        "adapters/cline_adapter.py",
        "adapters/copilot_adapter.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Error: Missing adapter files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        sys.exit(1)
    
    print("✅ All adapter files found!")
    print()
    
    # Run the multi-tool adapter
    print("🔧 Running multi-tool adapter...")
    print("   This will auto-detect and convert data from all AI coding assistants")
    print()
    
    try:
        # Change to adapters directory and run the multi-tool adapter
        os.chdir(adapters_dir)
        result = subprocess.run([sys.executable, "multi_tool_adapter.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print(result.stdout)
            print("✅ Adapter installation and conversion complete!")
            print()
            print("🎉 Sniffly is now a universal AI coding assistant analytics tool!")
            print()
            print("📊 You can now analyze data from:")
            print("   - GitHub Copilot (usage analytics)")
            print("   - Augment (conversation history)")
            print("   - Cline/Claude Dev (conversation history)")
            print("   - Standard Claude Code (if available)")
            print()
            print("🚀 Next steps:")
            print("   1. Run: sniffly init")
            print("   2. Open: http://127.0.0.1:8090")
            print("   3. Select any project to view analytics")
            
        else:
            print("❌ Error running multi-tool adapter:")
            print(result.stderr)
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
