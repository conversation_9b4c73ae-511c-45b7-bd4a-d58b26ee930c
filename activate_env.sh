#!/bin/bash
# Activation script for Sniffly development environment

echo "🐍 Activating Sniffly virtual environment..."
source venv/bin/activate

echo "✅ Virtual environment activated!"
echo "📊 Sniffly version: $(sniffly version)"
echo ""
echo "🚀 Quick commands:"
echo "  sniffly init          - Start the analytics dashboard"
echo "  sniffly config show   - Show current configuration"
echo "  sniffly help          - Show detailed help"
echo "  pytest                - Run tests"
echo "  ruff check            - Run linter"
echo "  mypy .                - Run type checker"
echo ""
echo "💡 To deactivate the environment, run: deactivate"
