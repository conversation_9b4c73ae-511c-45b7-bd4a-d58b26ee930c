<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sniffly Admin Dashboard</title>
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="/static/css/admin.css">
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="header-main">
                <div class="header-brand-with-info">
                    <img src="/static/images/logo.png" alt="Sniffly Logo" class="header-logo" onclick="window.location.href='/'" style="cursor: pointer;">
                    <div>
                        <h1>
                            <span class="brand-name" onclick="window.location.href='/'" style="cursor: pointer;">Sniffly</span>
                            <span class="header-subtitle">Admin Dashboard</span>
                        </h1>
                    </div>
                </div>
                <div class="header-nav">
                    <div class="admin-info">
                        <img id="admin-avatar" src="" alt="Admin" class="admin-avatar">
                        <span id="admin-email" class="admin-email"></span>
                        <a href="/admin/logout" class="nav-link">
                            <svg class="nav-icon" viewBox="0 0 16 16" width="16" height="16">
                                <path fill="currentColor" d="M12 9V7H8V5h4V3l4 3-4 3zm-2 3H6V3L2 1h8v3h1V1c0-.55-.45-1-1-1H1C.45 0 0 .45 0 1v11.38c0 .39.22.73.55.91L6 16.01V13h4c.55 0 1-.45 1-1V8h-1v4z"/>
                            </svg>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>
    
    <div class="container">
        <div class="admin-header">
            <h2>Public Gallery Management</h2>
            <div class="admin-stats">
                <div class="stat-item">
                    <span class="stat-label">Total Projects:</span>
                    <span id="total-count" class="stat-value">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Featured:</span>
                    <span id="featured-count" class="stat-value">0</span>
                </div>
            </div>
        </div>
        
        <div class="share-stats-section">
            <h3>Share Statistics</h3>
            <div class="share-stats-grid">
                <div class="stat-card clickable" onclick="showAllShares('all')">
                    <div class="stat-label">Total Shares</div>
                    <div id="total-shares" class="stat-large">0</div>
                    <div class="stat-breakdown">Active: <span id="total-active">0</span> | Deleted: <span id="total-deleted">0</span></div>
                </div>
                <div class="stat-card clickable" onclick="showAllShares('public')">
                    <div class="stat-label">Public</div>
                    <div id="public-shares" class="stat-large">0</div>
                    <div class="stat-breakdown">Active: <span id="public-active">0</span> | Deleted: <span id="public-deleted">0</span></div>
                </div>
                <div class="stat-card clickable" onclick="showAllShares('private')">
                    <div class="stat-label">Private</div>
                    <div id="private-shares" class="stat-large">0</div>
                    <div class="stat-breakdown">Active: <span id="private-active">0</span> | Deleted: <span id="private-deleted">0</span></div>
                </div>
                <div class="stat-card clickable" onclick="showAllShares('with-commands')">
                    <div class="stat-label">With Commands</div>
                    <div id="shares-with-commands" class="stat-large">0</div>
                    <div class="stat-breakdown">Active: <span id="commands-active">0</span> | Deleted: <span id="commands-deleted">0</span></div>
                </div>
            </div>
        </div>
        
        <div class="filters">
            <input type="text" id="search-input" placeholder="Search projects..." class="search-input">
            <select id="filter-select" class="filter-select">
                <option value="all">All Projects</option>
                <option value="featured">Featured Only</option>
                <option value="non-featured">Non-Featured Only</option>
                <option value="with-commands">With Commands</option>
                <option value="without-commands">Without Commands</option>
            </select>
        </div>
        
        <div id="projects-grid" class="admin-projects-grid">
            <div class="loading">Loading projects...</div>
        </div>
    </div>
    
    <!-- Modal for viewing all shares -->
    <div id="shares-modal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h2 id="shares-modal-title">All Shares</h2>
                <button class="close-btn" onclick="closeSharesModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="shares-search">
                    <input type="text" id="shares-search-input" placeholder="Search by project name, ID, or date..." class="search-input">
                    <div id="shares-count" class="shares-count">0 shares</div>
                </div>
                <div id="shares-list" class="shares-list">
                    <div class="loading">Loading shares...</div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="/static/js/admin.js"></script>
</body>
</html>