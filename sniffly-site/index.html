<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sniffly - <PERSON> Code Analytics</title>
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    <link rel="stylesheet" href="/static/css/style.css">
    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-XXXXXXXXXX');
    </script>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="header-main">
                <div class="header-brand-with-info">
                    <img src="/static/images/logo.png" alt="Sniffly Logo" class="header-logo" onclick="window.location.href='/'" style="cursor: pointer;">
                    <div>
                        <h1>
                            <span class="brand-name" onclick="window.location.href='/'" style="cursor: pointer;">Sniffly</span>
                            <span class="header-subtitle">Claude Code Analytics</span>
                        </h1>
                    </div>
                </div>
                <div class="header-nav">
                    <a href="https://github.com/chiphuyen/sniffly" target="_blank" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 16 16" width="16" height="16">
                            <path fill="currentColor" d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 *********.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 ********** 1.21 1.87.87 **********-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 *********-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 **********.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 **********.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .***********.38A8.013 8.013 0 0016 8c0-4.42-3.58-8-8-8z"/>
                        </svg>
                        GitHub
                    </a>
                    <a href="https://github.com/chiphuyen/sniffly/blob/main/docs/cli-reference.md" target="_blank" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 16 16" width="16" height="16">
                            <path fill="currentColor" d="M3 5h4v1H3V5zm0 3h4V7H3v1zm0 2h4V9H3v1zm11-5h-4v1h4V5zm0 2h-4v1h4V7zm0 2h-4v1h4V9zm2-6v9c0 .55-.45 1-1 1H9.5l-1 1-1-1H2c-.55 0-1-.45-1-1V3c0-.55.45-1 1-1h5.5l1 1 1-1H15c.55 0 1 .45 1 1zm-8 .5L7.5 3H2v9h6V3.5zm7-.5H9.5l-.5.5V12h6V3z"/>
                        </svg>
                        Docs
                    </a>
                </div>
            </div>
        </div>
    </header>
    
    <section class="install-compact">
        <div class="container">
            <div class="install-header">
                <h2>Insights to help you use Claude Code effectively</h2>
            </div>
            <div class="install-methods">
                <div class="install-option">
                    <code>uvx sniffly init</code>
                </div>
                <div class="install-divider">or</div>
                <div class="install-option">
                    <code>pip install sniffly && sniffly init</code>
                </div>
            </div>
        </div>
    </section>
    
    <section id="gallery" class="gallery">
        <div class="container">
            <h2>Public Dashboard Gallery</h2>
            <div id="gallery-grid" class="gallery-grid">
                <div class="loading">Loading projects...</div>
            </div>
        </div>
    </section>
    
    <footer>
        <div class="container">
            <p>&copy; 2025 Sniffly. Claude Code Analytics.</p>
            <p>
                <a href="https://github.com/chiphuyen/sniffly">GitHub</a> •
                <a href="https://github.com/chiphuyen/sniffly/issues">Issues</a> •
                <a href="https://github.com/chiphuyen/sniffly/blob/main/docs/cli-reference.md">Documentation</a>
            </p>
        </div>
    </footer>
    
    <script src="/static/js/gallery.js"></script>
</body>
</html>