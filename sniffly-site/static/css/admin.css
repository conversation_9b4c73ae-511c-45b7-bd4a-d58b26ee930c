/* Admin Dashboard Styles */

.admin-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.admin-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.admin-email {
    color: white;
    font-weight: 500;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.admin-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stat-label {
    color: var(--text-light);
    font-size: 0.9rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* Filters */
.filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.search-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 1rem;
}

.filter-select {
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 1rem;
    background: white;
    cursor: pointer;
}

/* Project Grid */
.admin-projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.admin-project-card {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    transition: all 0.2s;
}

.admin-project-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.admin-project-card.featured {
    border-color: #fbbf24;
    background: #fffbeb;
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.project-header h3 {
    margin: 0;
    color: var(--text-dark);
    font-size: 1.2rem;
}

.featured-badge {
    background: #fbbf24;
    color: #78350f;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
}

.project-info {
    margin-bottom: 1.5rem;
}

.info-row {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-row:last-child {
    border-bottom: none;
}

.info-row .label {
    color: var(--text-light);
    font-weight: 500;
}

/* Highlight rows with commands */
.info-row.with-commands {
    background: #e0f2fe;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    margin: 0 -0.75rem;
    font-weight: 600;
    color: #0369a1;
}

.info-row code {
    background: #f4f4f4;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-size: 0.85rem;
    font-family: 'Consolas', 'Monaco', monospace;
}

/* Action Buttons */
.project-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-block;
}

.btn-view {
    background: var(--primary-color);
    color: white;
}

.btn-view:hover {
    background: var(--secondary-color);
}

.btn-feature {
    background: #fbbf24;
    color: #78350f;
}

.btn-feature:hover {
    background: #f59e0b;
}

.btn-unfeature {
    background: #e5e7eb;
    color: #374151;
}

.btn-unfeature:hover {
    background: #d1d5db;
}

.btn-remove {
    background: #fee2e2;
    color: #dc2626;
}

.btn-remove:hover {
    background: #fecaca;
}

/* Share Statistics */
.share-stats-section {
    margin-bottom: 2rem;
    background: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.share-stats-section h3 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1.125rem;
}

.share-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.stat-card {
    background: var(--bg-light);
    padding: 1rem;
    border-radius: 0.375rem;
    text-align: center;
}

.stat-card .stat-label {
    font-size: 0.875rem;
    color: var(--text-light);
    margin-bottom: 0.5rem;
}

.stat-large {
    font-size: 2rem;
    font-weight: 600;
    color: var(--primary);
}

.stat-card.clickable {
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card.clickable:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stat-breakdown {
    font-size: 0.75rem;
    color: var(--text-light);
    margin-top: 0.5rem;
}

.stat-breakdown span {
    font-weight: 600;
    color: var(--text-dark);
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    color: var(--text-dark);
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-light);
    cursor: pointer;
    padding: 0;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    color: var(--text-dark);
}

.modal-body {
    padding: 1.5rem;
    overflow-y: auto;
    flex: 1;
}

/* Shares Search */
.shares-search {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    align-items: center;
}

.shares-search .search-input {
    flex: 1;
}

.shares-count {
    color: var(--text-light);
    font-size: 0.9rem;
    white-space: nowrap;
}

/* Shares List */
.shares-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-height: 50vh;
    overflow-y: auto;
}

.share-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: 6px;
}

.share-info {
    flex: 1;
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 1rem;
    align-items: center;
}

.share-project {
    font-weight: 600;
    color: var(--text-dark);
}

.share-date {
    color: var(--text-light);
    font-size: 0.9rem;
}

.share-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    text-align: center;
}

.badge-public {
    background: #d1fae5;
    color: #065f46;
}

.badge-private {
    background: #e5e7eb;
    color: #374151;
}

.badge-commands {
    background: #dbeafe;
    color: #1e40af;
}

.share-actions {
    display: flex;
    gap: 0.5rem;
}

/* States */
.loading, .error, .no-results {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem;
    color: var(--text-light);
}

.error {
    color: #dc2626;
}

/* Responsive */
@media (max-width: 768px) {
    .admin-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .filters {
        flex-direction: column;
    }
    
    .admin-projects-grid {
        grid-template-columns: 1fr;
    }
    
    .admin-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}