* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: relative;
    min-height: 100px;
}

.header-content {
    max-width: 1600px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
}

.header-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

/* Special alignment for overview page where we have 2 lines */
.header-brand-with-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-logo {
    width: 48px;
    height: 48px;
    object-fit: contain;
    background: rgba(255, 255, 255, 0.15);
    padding: 0px;
    border-radius: 8px;
}

/* Larger logo for overview page to match both text lines */
.header-brand-with-info .header-logo {
    width: 54px;
    height: 54px;
    flex-shrink: 0;
    align-self: center; /* Center vertically with the text block */
}

.header h1 {
    font-size: 1.75rem;
    margin: 0;
    display: flex;
    align-items: baseline;
    gap: 0.75rem;
}

.brand-name {
    font-weight: 600;
    font-size: 1.75rem;
    letter-spacing: -0.02em;
}

.header-subtitle {
    font-weight: 400;
    font-size: 1.1rem;
    opacity: 0.85;
    margin-left: 0.25rem;
}

.brand-name::after {
    content: "";
    display: inline-block;
    width: 1px;
    height: 0.5em;
    background: rgba(255, 255, 255, 0.3);
    margin: 0 0.6rem;
    vertical-align: middle;
}

.header p {
    font-size: 0.9rem;
    opacity: 0.85;
    margin: 0;
    font-weight: 400;
}

#project-info {
    margin-left: 4.5rem; /* Align with the text, not the logo */
}

/* In overview page, project-info is nested differently */
.header-brand-with-info #project-info {
    margin-left: 0;
    margin-top: 0.25rem;
}

.header-brand-with-info h1 {
    line-height: 1.2;
    margin-bottom: 0;
}

.container {
    max-width: 1600px;
    margin: 2rem auto;
    padding: 0 1rem;
}

/* Overview Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Force 3-column layout when there are exactly 6 cards on larger screens */
@media (min-width: 1024px) {
    .stats-grid:has(.stat-card:nth-child(6)):not(:has(.stat-card:nth-child(7))) {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Force 2-column layout on medium screens */
@media (min-width: 768px) and (max-width: 1023px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Single column on small screens */
@media (max-width: 767px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.stat-card h3 {
    color: #666;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
}

.stat-card .value {
    font-size: 2.5rem;
    font-weight: bold;
    color: #667eea;
}

.stat-card .subtext {
    font-size: 0.9rem;
    color: #999;
    margin-top: 0.5rem;
}

.stat-card .breakdown {
    margin-top: 1rem;
    font-size: 0.85rem;
    color: #666;
}

.stat-card .breakdown span {
    display: inline-block;
    margin: 0.2rem;
    padding: 0.2rem 0.5rem;
    background: #f0f0f0;
    border-radius: 15px;
}


/* Charts Section */
.charts-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.chart-container {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    min-height: 450px;
    display: flex;
    flex-direction: column;
}

.chart-container canvas {
    flex: 1;
    max-height: 320px;
}

.chart-container h2 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.chart-subtitle {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 1rem;
    position: relative;
}

.chart-subtitle .separator {
    margin: 0 0.5rem;
    color: #ccc;
}

.chart-subtitle .token-input {
    color: #667eea;
    font-weight: 500;
}

.chart-subtitle .token-output {
    color: #764ba2;
    font-weight: 500;
}

.chart-subtitle .token-cache {
    color: #48bb78;
    font-weight: 500;
}

.chart-subtitle .tooltip-info-icon {
    font-size: 0.8rem;
    color: #999;
    cursor: help;
    margin-left: 0.25rem;
    display: inline-block;
    vertical-align: baseline;
}

.chart-subtitle .tooltip-info-icon:hover {
    color: #667eea;
}


/* Messages Table Specific Styles (removing duplicates) */
/* Column widths for message table */
.message-table th:nth-child(1), 
.message-table td:nth-child(1) { width: 120px; } /* Type */
.message-table th:nth-child(2), 
.message-table td:nth-child(2) { width: 35%; max-width: 500px; } /* Message */
.message-table th:nth-child(3), 
.message-table td:nth-child(3) { width: 150px; } /* Timestamp */
.message-table th:nth-child(4), 
.message-table td:nth-child(4) { width: 100px; } /* Model */
.message-table th:nth-child(5), 
.message-table td:nth-child(5) { width: 120px; } /* Tokens */
.message-table th:nth-child(6), 
.message-table td:nth-child(6) { width: 100px; } /* Session */
.message-table th:nth-child(7), 
.message-table td:nth-child(7) { width: auto; } /* Tools */

/* Type badges */
.type-badge {
    display: inline-block;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: lowercase;
}

.type-badge.user {
    background: #e3f2fd;
    color: #1976d2;
}

.type-badge.assistant {
    background: #e8f5e9;
    color: #388e3c;
}

.type-badge.task {
    background: #f3e5f5;
    color: #7b1fa2;
}

.type-badge.tool-result {
    background: #fff3e0;
    color: #f57c00;
}

.type-badge.summary {
    background: #f3e5f5;
    color: #6a1b9a;
}

.type-badge.compact_summary {
    background: #e8eaf6;
    color: #3f51b5;
}

/* Message content - see consolidated definition below (line 363) */

/* Session ID */
.session-id {
    font-family: monospace;
    font-size: 0.85rem;
    color: #666;
    cursor: pointer;
    position: relative;
}

.session-id:hover {
    color: #667eea;
}

.session-id:hover::after {
    content: attr(data-full-id);
    position: absolute;
    bottom: 100%;
    left: 0;
    background: #333;
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 10;
    margin-bottom: 0.2rem;
}

/* Tools */
.tools-list {
    display: flex;
    gap: 0.3rem;
    flex-wrap: wrap;
}

.tool-chip {
    background: #f0f0f0;
    padding: 0.1rem 0.4rem;
    border-radius: 10px;
    font-size: 0.75rem;
    color: #666;
}

/* Tokens */
.tokens {
    font-family: monospace;
    font-size: 0.85rem;
    color: #666;
    white-space: nowrap;
}


/* Message detail modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    overflow-y: auto;
}

.modal-content {
    background: white;
    margin: 2rem auto;
    padding: 2rem;
    max-width: 800px;
    border-radius: 10px;
    position: relative;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

.modal-close:hover {
    color: #333;
}

/* Modal navigation */
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.modal-navigation {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.modal-nav-btn {
    padding: 0.25rem 0.75rem;
    background: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;
    transition: background-color 0.2s;
}

.modal-nav-btn:hover:not(:disabled) {
    background: #e0e0e0;
}

.modal-nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.modal-position {
    font-size: 0.875rem;
    color: #666;
    white-space: nowrap;
}

.detail-grid {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.detail-label {
    font-weight: 600;
    color: #666;
}

.detail-value {
    color: #333;
}

.detail-content {
    background: #f8f8f8;
    padding: 1rem;
    border-radius: 6px;
    white-space: pre-wrap;
    font-family: monospace;
    font-size: 0.9rem;
    max-height: 400px;
    overflow-y: auto;
}

.detail-tools {
    margin-top: 1rem;
}

.detail-tool {
    background: #f0f0f0;
    padding: 1rem;
    margin-bottom: 0.5rem;
    border-radius: 6px;
}

.detail-tool-name {
    font-weight: 600;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.detail-tool-input {
    font-family: monospace;
    font-size: 0.85rem;
    color: #666;
    white-space: pre-wrap;
}

/* Error indicator */
.error-indicator {
    color: #d32f2f;
    font-weight: bold;
}

/* Message content styling */
.message-content {
    max-width: 500px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    cursor: pointer;
}

.message-content:hover {
    color: #667eea;
}

/* Table layout fixes */
.message-table {
    table-layout: fixed;
}

.message-table td {
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 0; /* Force respect of percentage widths */
}


/* Error Indicator */
.error-indicator {
    color: #e53e3e;
    font-weight: bold;
    margin-right: 0.25rem;
}

.message-content .error-indicator {
    font-size: 1.1em;
}

/* Loading */
.loading {
    text-align: center;
    padding: 2rem;
    color: #666;
}

/* Tab Styles */
.tabs-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-top: 2rem;
    overflow: hidden;
}

.tab-nav {
    display: flex;
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
}

.tab-button {
    flex: 1;
    padding: 1rem 2rem;
    background: none;
    border: none;
    font-size: 1rem;
    font-weight: 600;
    color: #666;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
}

.tab-button:hover {
    background: #e9ecef;
    color: #333;
}

.tab-button.active {
    color: #667eea;
    background: white;
}

.tab-button.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: #667eea;
}

.tab-content {
    display: none;
    padding: 2rem;
}

.tab-content.active {
    display: block;
}

/* Remove margins from sections when in tabs */
.tabs-container .user-commands-section,
.tabs-container .messages-section {
    margin-top: 0 !important;
    box-shadow: none;
    background: none;
    padding: 0;
}

/* JSONL Viewer Specific Styles */
.jsonl-viewer {
    height: 600px;
    overflow-y: auto;
}

.jsonl-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-bottom: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

/* JSONL Table Styles */
.jsonl-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

/* Sticky header for JSONL table */
.jsonl-table th {
    position: sticky;
    top: 0;
    z-index: 10;
    background: #f8f9fa;
    padding: 0.75rem;
    text-align: left;
    font-weight: 600;
    color: #555;
    border-bottom: 2px solid #e0e0e0;
}

.jsonl-table td {
    padding: 0.5rem 0.75rem;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: top;
}

.jsonl-table tr {
    cursor: pointer;
}

.jsonl-table tr:hover {
    background: #f8f9fa;
}

.line-number {
    font-weight: 600;
    color: #666;
    width: 60px;
}

/* Type badge duplicate removed - using definition from line 164 */

.type-user {
    background: #e3f2fd;
    color: #1976d2;
}

.type-assistant {
    background: #f3e5f5;
    color: #7b1fa2;
}

.type-summary {
    background: #fff3e0;
    color: #f57c00;
}

.content-preview {
    max-width: 600px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #444;
}


/* Shared Tooltip Styles */
.tooltip-dark {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 0.75rem;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    font-size: 0.85rem;
    line-height: 1.4;
    display: none;
}

/* Tooltip positioning variants */
.tooltip-dark.position-below {
    margin-top: 0.5rem;
}

.tooltip-dark.position-above {
    bottom: 100%;
    margin-bottom: 0.5rem;
    transform: translateX(-50%);
    left: 50%;
}

.tooltip-dark.position-right {
    left: 100%;
    margin-left: 0.5rem;
}

.tooltip-dark.position-left {
    right: 100%;
    margin-right: 0.5rem;
}

/* Info icon style */
.tooltip-info-icon {
    cursor: help;
    color: #999;
    font-size: 0.8rem;
    font-weight: normal;
    margin-left: 0.25rem;
    position: relative;
    display: inline-block;
}

/* ========================================
 * UTILITY CLASSES (Phase 1)
 * ======================================== */

/* Typography Utilities */
.text-sm { font-size: 0.85rem; }
.text-base { font-size: 0.9rem; }
.text-lg { font-size: 1rem; }
.text-muted { color: #666; }
.text-secondary { color: #999; }
.text-white-95 { color: rgba(255, 255, 255, 0.95); }
.opacity-90 { opacity: 0.9; }
.text-sm-muted { font-size: 0.9rem; color: #666; }

/* Spacing Utilities */
.mb-0 { margin-bottom: 0; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mt-8 { margin-top: 2rem; }
.p-2 { padding: 0.5rem; }
.p-4 { padding: 1rem; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }

/* Layout Utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-4 { gap: 1rem; }

/* Common flex patterns */
.flex-center { 
  display: flex; 
  align-items: center; 
  justify-content: center; 
}
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* ========================================
 * COMPONENT STYLES
 * ======================================== */

/* Header Buttons Container */
.header-controls {
    position: absolute;
    right: 2rem;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

/* Button Styles */
.btn-header {
    padding: 0.4rem 0.9rem;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    display: none; /* Hidden by default, shown when data loads */
    font-size: 0.9rem;
    white-space: nowrap;
}

.btn-header:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Export button styles */
.btn-export {
    padding: 0.4rem 0.8rem;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    margin-left: 0.5rem;
}

.btn-export:hover {
    background: #5a67d8;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Tooltip Width Classes */
.tooltip-sm { width: 280px; }

/* ========================================
 * UNIFIED TABLE SYSTEM
 * ======================================== */

/* Table Container - Single unified class for all tables */
.table-container {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    max-width: 1400px;
    margin: 2rem auto;
}

/* Table Header Layout */
.table-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.table-header h2 {
    font-size: 1.5rem;
    color: #333;
    margin: 0;
}

.table-info {
    color: #666;
    font-size: 0.9rem;
}

/* Table Controls */
.table-controls {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    align-items: center;
}

/* Filter Groups */
.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 600;
    color: #555;
    font-size: 0.9rem;
}

.filter-group input,
.filter-group select {
    padding: 0.4rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.filter-search {
    width: 200px;
}

.filter-select-wide {
    min-width: 300px;
    max-width: 600px;
}

/* Row Jump Controls */
.row-jump {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.row-jump input {
    width: 80px;
    padding: 0.25rem 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.row-jump button {
    padding: 0.25rem 0.75rem;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background 0.2s;
}

.row-jump button:hover {
    background: #f0f0f0;
}

/* Table Wrapper */
.table-wrapper {
    overflow-x: visible;
    overflow-y: visible;
    /* max-height: 600px; - removed to disable scrolling */
    margin-bottom: 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
}

/* Base Table Styles */
table.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.data-table th {
    background: #f8f8f8;
    text-align: left;
    padding: 0.75rem;
    font-weight: 600;
    color: #555;
    border-bottom: 2px solid #e0e0e0;
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 10;
}

/* Sortable columns */
.data-table th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
    padding-right: 1.5rem;
}

.data-table th.sortable:hover {
    background: #f0f0f0;
}

/* Sort indicators */
.data-table th.sortable::after {
    content: ' ↕';
    color: #ccc;
    font-size: 0.8em;
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
}

.data-table th.sort-asc::after {
    content: ' ↑';
    color: #667eea;
}

.data-table th.sort-desc::after {
    content: ' ↓';
    color: #667eea;
}

/* Table cells */
.data-table td {
    padding: 0.5rem 0.75rem;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: top;
}

.data-table tr:hover {
    background: #f8f9fa;
}

/* Pagination */
.table-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 1.5rem;
}

.table-pagination button {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.table-pagination button:hover:not(:disabled) {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.table-pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.page-info input {
    width: 60px;
    padding: 0.3rem;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Loading States */
.table-loading {
    text-align: center;
    padding: 3rem;
    color: #666;
}

/* Empty States */
.table-empty {
    text-align: center;
    padding: 3rem;
    color: #999;
}

.empty-state {
    text-align: center;
    padding: 4rem;
    color: #999;
}

.empty-state h2 {
    color: #666;
    margin-bottom: 1rem;
}

/* Metadata Display */
.metadata-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.metadata-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.metadata-label {
    font-weight: 600;
    color: #555;
    font-size: 0.85rem;
}

.metadata-value {
    color: #666;
    font-size: 0.9rem;
}

.metadata-value.mono {
    font-family: monospace;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }