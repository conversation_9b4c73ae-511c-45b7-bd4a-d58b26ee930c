/* Project Selector Component Styles */

.project-selector-container {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    width: 100%;
}

.project-selector-main {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.project-selector-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-family: '<PERSON>sol<PERSON>', 'Monaco', 'Courier New', monospace;
}

.project-selector-label {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.95);
    font-weight: bold;
}

.project-selector-dropdown {
    padding: 0.35rem 0.75rem;
    background: rgba(255, 255, 255, 0.15);
    color: #fff;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    font-size: 0.875rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    min-width: 280px;
    max-width: 450px;
    transition: all 0.2s;
}

.project-selector-dropdown:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

.project-selector-dropdown:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

.project-selector-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

.project-selector-status-text {
    font-size: 0.9rem;
    opacity: 0.9;
}

.project-selector-info {
    position: relative;
    display: inline-block;
}

.project-selector-info-icon {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8rem;
}