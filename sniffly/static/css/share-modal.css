/* Share Modal Styles */

/* Modal content customization */
.modal-content.share-modal {
    max-width: 500px;
}

.modal-content.share-link-modal {
    max-width: 600px;
}

/* Share modal title */
.share-modal-title {
    margin-bottom: 0.5rem;
}

/* Share modal description */
.share-modal-description {
    color: #666;
    margin-bottom: 1.5rem;
}

/* Share options container */
.share-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin: 1.5rem 0;
}

/* Share option container */
.share-option-container {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
}

.share-option-container:hover:not([style*="cursor: not-allowed"]) {
    background: #f8f9fa;
    border-color: #667eea;
}

.share-checkbox {
    margin-top: 0.25rem;
    cursor: pointer;
}

.share-option-label {
    font-weight: 600;
    font-size: 0.95rem;
}

.share-option-description {
    color: #666;
    line-height: 1.4;
    font-size: 0.875rem;
}

/* Share info section */
.share-info {
    border-top: 1px solid #e0e0e0;
    margin-top: 1rem;
}

/* Modal button container */
.modal-button-container {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

/* Modal buttons */
.modal-button {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s;
}

.modal-button-cancel {
    background: #f0f0f0;
    color: #333;
}

.modal-button-cancel:hover {
    background: #e0e0e0;
}

.modal-button-primary {
    background: #667eea;
    color: white;
    font-weight: 600;
}

.modal-button-primary:hover {
    background: #5a67d8;
}

/* Share link display */
.share-link-container {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    margin: 1.5rem 0;
}

.share-link-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: monospace;
    font-size: 0.9rem;
}

.share-link-input:focus {
    outline: none;
    border-color: #667eea;
}

/* Share link footer */
.share-link-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.share-link-info {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
}

.share-link-info-icon {
    margin-right: 0.25rem;
}

/* Done button */
.modal-button-done {
    padding: 0.5rem 1.5rem;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: background 0.2s;
}

.modal-button-done:hover {
    background: #5a67d8;
}