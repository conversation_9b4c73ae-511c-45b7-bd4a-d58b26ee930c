# Claude Analytics Configuration

# Memory Cache Settings
# Maximum number of projects to keep in memory cache
CACHE_MAX_PROJECTS=5

# Maximum size in MB for a single project in memory cache
# Projects larger than this will not be cached
CACHE_MAX_MB_PER_PROJECT=500

# Number of recent projects to pre-warm on server startup
CACHE_WARM_ON_STARTUP=3

# Frontend Settings
# Number of messages to load when user clicks on Messages tab
MESSAGES_INITIAL_LOAD=1000

# Enable memory monitoring in browser console (shows memory usage breakdown)
# Note: This uses JSON.stringify which can slow down the app with large datasets
ENABLE_MEMORY_MONITOR=false

# Logging Configuration
# Set the logging level: DEBUG, INFO, WARNING, ERROR, CRITICAL
# Default: INFO
LOG_LEVEL=INFO

# Background Processing
# Automatically process all projects in the background to cache their stats
# This enables instant loading of stats for all projects in the overview
# Default: true
ENABLE_BACKGROUND_PROCESSING=true

# Examples:
# For a system with lots of RAM and fast disk:
# CACHE_MAX_PROJECTS=10
# CACHE_MAX_MB_PER_PROJECT=1000
# CACHE_WARM_ON_STARTUP=5
# MESSAGES_INITIAL_LOAD=2000

# For a constrained system:
# CACHE_MAX_PROJECTS=3
# CACHE_MAX_MB_PER_PROJECT=200
# CACHE_WARM_ON_STARTUP=1
# MESSAGES_INITIAL_LOAD=500
