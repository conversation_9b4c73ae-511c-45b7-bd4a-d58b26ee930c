"""Tests for CLI commands."""
import json
import os
from pathlib import Path
from unittest.mock import patch

import pytest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from sniffly.cli import cli
from sniffly.config import Config


class TestCLICommands:
    """Test CLI commands."""
    
    def setup_method(self):
        """Set up test environment."""
        self.runner = CliRunner()
        
    def test_version_command(self):
        """Test version command shows version."""
        result = self.runner.invoke(cli, ['version'])
        assert result.exit_code == 0
        assert 'sniffly v' in result.output
        
    def test_help_command(self):
        """Test help command shows usage."""
        result = self.runner.invoke(cli, ['help'])
        assert result.exit_code == 0
        assert 'Sniffly - Claude Code Analytics Tool' in result.output
        assert 'Usage Examples:' in result.output
        assert 'Configuration Keys:' in result.output
        
    def test_config_show(self):
        """Test config show command."""
        with self.runner.isolated_filesystem():
            # Create isolated config directory
            config_dir = Path('.sniffly')
            config_dir.mkdir()
            
            with patch('sniffly.config.Path.home') as mock_home:
                mock_home.return_value = Path('.')
                result = self.runner.invoke(cli, ['config', 'show'])
                assert result.exit_code == 0
                assert 'Current configuration:' in result.output
                assert 'port: 8081' in result.output
                assert '(default)' in result.output
            
    def test_config_show_json(self):
        """Test config show with JSON output."""
        with self.runner.isolated_filesystem():
            # Create isolated config directory
            config_dir = Path('.sniffly')
            config_dir.mkdir()
            
            with patch('sniffly.config.Path.home') as mock_home:
                mock_home.return_value = Path('.')
                result = self.runner.invoke(cli, ['config', 'show', '--json'])
                assert result.exit_code == 0
                config_data = json.loads(result.output)
                assert config_data['port'] == 8081
                assert config_data['auto_browser'] is True
            
    def test_config_set(self):
        """Test config set command."""
        with self.runner.isolated_filesystem():
            # Set a value
            result = self.runner.invoke(cli, ['config', 'set', 'port', '8090'])
            assert result.exit_code == 0
            assert '✅ Set port = 8090' in result.output
            
            # Verify it was set
            result = self.runner.invoke(cli, ['config', 'show', '--json'])
            config_data = json.loads(result.output)
            assert config_data['port'] == 8090
            
    def test_config_set_boolean(self):
        """Test config set with boolean value."""
        with self.runner.isolated_filesystem():
            # Test various boolean representations
            for value in ['true', 'True', '1', 'yes', 'on']:
                result = self.runner.invoke(cli, ['config', 'set', 'auto_browser', value])
                assert result.exit_code == 0
                
            result = self.runner.invoke(cli, ['config', 'show', '--json'])
            config_data = json.loads(result.output)
            assert config_data['auto_browser'] is True
            
            # Test false values
            for value in ['false', 'False', '0', 'no', 'off']:
                result = self.runner.invoke(cli, ['config', 'set', 'auto_browser', value])
                assert result.exit_code == 0
                
            result = self.runner.invoke(cli, ['config', 'show', '--json'])
            config_data = json.loads(result.output)
            assert config_data['auto_browser'] is False
            
    def test_config_set_invalid_key(self):
        """Test config set with invalid key."""
        result = self.runner.invoke(cli, ['config', 'set', 'invalid_key', 'value'])
        assert result.exit_code == 0
        assert "Error: Unknown configuration key 'invalid_key'" in result.output
        assert 'Valid keys:' in result.output
        
    def test_config_set_invalid_integer(self):
        """Test config set with invalid integer value."""
        result = self.runner.invoke(cli, ['config', 'set', 'port', 'not_a_number'])
        assert result.exit_code == 0
        assert 'Error: port must be an integer' in result.output
        
    def test_config_unset(self):
        """Test config unset command."""
        with self.runner.isolated_filesystem():
            # Set a value first
            self.runner.invoke(cli, ['config', 'set', 'port', '8090'])
            
            # Unset it
            result = self.runner.invoke(cli, ['config', 'unset', 'port'])
            assert result.exit_code == 0
            assert '✅ Removed port from config file' in result.output
            
            # Verify it's back to default
            result = self.runner.invoke(cli, ['config', 'show'])
            assert 'port: 8081 (default)' in result.output
            
    def test_clear_cache_placeholder(self):
        """Test clear-cache command (placeholder for now)."""
        result = self.runner.invoke(cli, ['clear-cache'])
        assert result.exit_code == 0
        assert 'Cache clearing requires the server to be running' in result.output
        
    def test_init_command(self):
        """Test init command starts server."""
        # Skip this test - it's too complex to mock properly
        # The init command is integration tested manually
        pytest.skip("Init command requires full server import - tested manually")
            
    def test_init_with_custom_port(self):
        """Test init command with custom port."""  
        # Skip this test - it's too complex to mock properly
        # The init command is integration tested manually
        pytest.skip("Init command requires full server import - tested manually")
            
    def test_config_environment_override(self):
        """Test that environment variables override config file."""
        with self.runner.isolated_filesystem():
            # Set config file value
            self.runner.invoke(cli, ['config', 'set', 'port', '8090'])
            
            # Set environment variable
            with patch.dict(os.environ, {'PORT': '9000'}):
                result = self.runner.invoke(cli, ['config', 'show'])
                assert 'port: 9000 (from environment)' in result.output
                
                
class TestConfig:
    """Test Config class directly."""
    
    def test_defaults(self):
        """Test default configuration values."""
        with CliRunner().isolated_filesystem():
            # Create isolated config in current directory
            config_dir = Path('.sniffly')
            cfg = Config(config_dir=config_dir)
            assert cfg.get('port') == 8081
            assert cfg.get('auto_browser') is True
            assert cfg.get('cache_max_projects') == 5
            
    def test_config_file_persistence(self):
        """Test configuration persists to file."""
        with CliRunner().isolated_filesystem():
            # Create isolated config in current directory
            config_dir = Path('.sniffly')
            cfg = Config(config_dir=config_dir)
            cfg.set('port', 9000)
            
            # Create new instance to test persistence
            cfg2 = Config(config_dir=config_dir)
            assert cfg2.get('port') == 9000
            
    def test_environment_override(self):
        """Test environment variables override config."""
        with CliRunner().isolated_filesystem():
            # Create isolated config in current directory
            config_dir = Path('.sniffly')
            cfg = Config(config_dir=config_dir)
            cfg.set('port', 8090)
            
            with patch.dict(os.environ, {'PORT': '9000'}):
                assert cfg.get('port') == 9000
                
    def test_parse_boolean_values(self):
        """Test boolean value parsing."""
        cfg = Config()
        
        # Test true values
        for value in ['true', 'True', '1', 'yes', 'on']:
            assert cfg._parse_value(value, 'auto_browser') is True
            
        # Test false values
        for value in ['false', 'False', '0', 'no', 'off', 'anything_else']:
            assert cfg._parse_value(value, 'auto_browser') is False
            
    def test_parse_integer_values(self):
        """Test integer value parsing."""
        cfg = Config()
        
        assert cfg._parse_value('123', 'port') == 123
        assert cfg._parse_value('invalid', 'port') == 8081  # Returns default
        
    def test_get_all_configuration(self):
        """Test getting all configuration values."""
        with CliRunner().isolated_filesystem():
            # Create isolated config in current directory
            config_dir = Path('.sniffly')
            cfg = Config(config_dir=config_dir)
            cfg.set('port', 9000)
            
            all_config = cfg.get_all()
            assert all_config['port'] == 9000
            assert all_config['auto_browser'] is True
            assert len(all_config) == len(Config.DEFAULTS)