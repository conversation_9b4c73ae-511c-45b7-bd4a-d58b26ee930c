[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "sniffly"
version = "0.1.4"
description = "Claude Code Analytics Dashboard"
readme = "README.md"
requires-python = ">=3.10"
license = "MIT"
authors = [
    { name = "<PERSON> Huyen", email = "<EMAIL>" },
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "fastapi>=0.100.0",
    "uvicorn[standard]>=0.23.0",
    "click>=8.0.0",
    "python-dotenv>=1.0.0",
    "python-dateutil>=2.8.2",
    "httpx>=0.24.0",
    "uvloop>=0.17.0;sys_platform!='win32'",
    "winloop>=0.1.8;sys_platform=='win32'",
    "python-multipart>=0.0.6",
    "aiofiles>=23.0.0",
    "orjson>=3.9.0",
    "boto3>=1.26.0",
]

[project.scripts]
sniffly = "sniffly.cli:cli"

[project.urls]
Homepage = "https://sniffly.dev"
Documentation = "https://sniffly.dev/docs"
Repository = "https://github.com/chiphuyen/sniffly"
Issues = "https://github.com/chiphuyen/sniffly/issues"

[tool.hatch.build.targets.wheel]
packages = ["sniffly"]

[tool.hatch.build.targets.wheel.shared-data]
"sniffly/static" = "sniffly/static"
"sniffly/templates" = "sniffly/templates"

[tool.ruff]
line-length = 120
target-version = "py311"
exclude = [
    "venv/",
    "htmlcov/",
    ".pytest_cache/",
    "__pycache__/",
    "build/",
    "dist/",
]

[tool.ruff.lint]
select = ["E", "F", "I", "N", "UP", "S", "B", "A", "C4", "T10", "T20"]
ignore = [
    "S324",  # Insecure hash functions - MD5 is used for cache keys, not security
]

[tool.ruff.lint.per-file-ignores]
# Allow print statements and asserts in tests
"tests/**/*.py" = ["T201", "S101"]
"test_*.py" = ["T201", "S101"]
"*_test.py" = ["T201", "S101"]


[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
check_untyped_defs = true
exclude = [
    "venv/",
    "htmlcov/",
    "tests/",
    "setup.py",
]

[[tool.mypy.overrides]]
module = [
    "dateutil.*",
    "dotenv",
    "fastapi.*",
    "uvicorn.*",
    "orjson",
    "psutil",
]
ignore_missing_imports = true